from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QRect, QEasingCurve
from PySide6.QtGui import QPixmap
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
import logging
logger = logging.getLogger(__name__)

class PhonePermissionNotification(QWidget):
    def __init__(self, camera_model, parent=None):
        super().__init__(parent)
        self.camera_model = camera_model
        self.camera_name = camera_model.name if camera_model else "Camera"
        self.camera_id = camera_model.get_property("id") if camera_model else "Unknown"
        self.parent = parent
        self.is_show = True

        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setup_ui()
        self.setup_animation()

    def setup_ui(self):
        # Similar to Notifications class but with custom buttons
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        self.widget = QWidget()
        self.widget.setObjectName('widget_main')
        self.widget.setStyleSheet(f"""
            QWidget#widget_main {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                border-radius: 8px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_color")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
            }}
        """)

        layout = QVBoxLayout(self.widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # Message with icon
        message_layout = QHBoxLayout()

        # Message text
        message_text = self.tr("{} is connected. Do you want to grant permission to access camera?").format(self.camera_name)
        message = QLabel(message_text)
        message.setWordWrap(True)
        message.setStyleSheet(f'''
            font-size: 13px;
            color: {main_controller.get_theme_attribute("Color", "label_title_1")};
        ''')

        message_layout.addWidget(message)
        layout.addLayout(message_layout)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        accept_btn = QPushButton("✓")
        accept_btn.setFixedSize(30, 30)
        accept_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        accept_btn.clicked.connect(self.accept_permission)

        decline_btn = QPushButton("✗")
        decline_btn.setFixedSize(30, 30)
        decline_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        decline_btn.clicked.connect(self.decline_permission)

        button_layout.addStretch()
        button_layout.addWidget(accept_btn)
        button_layout.addWidget(decline_btn)
        layout.addLayout(button_layout)

        main_layout.addWidget(self.widget)

    def setup_animation(self):
        # Similar to Notifications class
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

        self.adjustSize()
        self.start_animation()

        # Auto-hide after 5 seconds
        self.auto_hide_timer = QTimer(self)
        self.auto_hide_timer.setSingleShot(True)
        self.auto_hide_timer.timeout.connect(self.auto_hide)
        self.auto_hide_timer.start(5000)  # 5 seconds

    def start_animation(self):
        if self.is_show:
            self.is_show = False
            self.show()

            center_x = (self.parent.width() - self.width()) // 2
            start_y = -self.height()
            end_y = 20

            self.animation.setStartValue(QRect(center_x, start_y, self.width(), self.height()))
            self.animation.setEndValue(QRect(center_x, end_y, self.width(), self.height()))
            self.animation.start()

    def accept_permission(self):
        # Stop auto-hide timer
        if hasattr(self, 'auto_hide_timer'):
            self.auto_hide_timer.stop()

        if self.camera_model:
            self.camera_model.permissionGranted = True

            # Start camera stream using notify_camera_state_change (consistent with grid button)
            from src.common.camera.video_player_manager import video_player_manager
            video_player_manager.notify_camera_state_change(self.camera_model, "CONNECTED")
        self.close_with_animation()

    def decline_permission(self):
        # Stop auto-hide timer
        if hasattr(self, 'auto_hide_timer'):
            self.auto_hide_timer.stop()

        # Do nothing, keep permission denied
        self.close_with_animation()

    def auto_hide(self):
        """Auto-hide notification after timeout"""
        self.close_with_animation()

    def close_with_animation(self):
        center_x = (self.parent.width() - self.width()) // 2
        current_y = self.y()

        self.animation.setStartValue(QRect(center_x, current_y, self.width(), self.height()))
        self.animation.setEndValue(QRect(center_x, -self.height(), self.width(), self.height()))
        self.animation.finished.connect(self.cleanup_and_close)
        self.animation.start()

    def cleanup_and_close(self):
        """Cleanup notification from tracking and close"""
        camera_id = self.camera_model.get_property("id") if self.camera_model else "Unknown"

        # Remove from active notifications tracking
        from src.common.widget.notifications.listen_message_notifications import listen_show_notification
        if camera_id in listen_show_notification.active_phone_notifications:
            del listen_show_notification.active_phone_notifications[camera_id]

        self.close()
