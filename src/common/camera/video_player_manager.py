

import time
import av
import time
import uuid
import threading
from queue import Queue
from typing import Callable
from typing import List
from PySide6.QtCore import QObject
from PySide6.QtWidgets import QWidget
from src.common.model.camera_model import CameraModel
from src.common.server.server_info import ServerInfoModel
from src.common.controller.main_controller import main_controller
from src.common.camera.player import StreamCameraType
from src.common.camera.live_stream_player import LiveStreamPlayer
from src.common.camera.video_player import VideoPlayer
from src.common.qml.models.common_enum import CommonEnum
from src.common.threads.thread_pool_manager import threadPoolManager
import logging
logger = logging.getLogger(__name__)
logging.basicConfig()
logging.getLogger('libav').setLevel(level=logging.CRITICAL)
av.logging.set_level(av.logging.PANIC)

class VideoPlayerManager(QObject):
    __instance = None
    def __init__(self, parent=None, target: Callable = None, args=()):
        super().__init__(parent)
        self._cleanup_lock = threading.Lock()
        self.target = target
        self.args = args
        self.input_queue = Queue()
        self.list_player = {}
        self.list_widgets = {}
        threadPoolManager.create_pool("VideoPlayerManager",max_threads=64)

    @staticmethod
    def get_instance():
        if VideoPlayerManager.__instance is None:
            VideoPlayerManager.__instance = VideoPlayerManager()
        return VideoPlayerManager.__instance

    def process_player(self,widget,camera_model,stream_type):
        player = self.get_player(camera_model=camera_model,stream_type=stream_type)
        widget.process_player_signal.emit(player)
        return True
    
    def register_player(self,widget:QWidget = None,camera_model:CameraModel = None,stream_type = CommonEnum.StreamType.MAIN_STREAM):
        threadPoolManager.run("VideoPlayerManager",self.process_player,args=(widget,camera_model,stream_type,))

    def unregister_player(self,widget:QWidget):
        if widget is not None:
            player = widget.player
            camera_model:CameraModel = player.camera_model
            stream_type = player.stream_type
            logger.info(f'unregister_player: {camera_model.get_property("id", None)},{stream_type} - {player}')
            ok = player.unregister_signal(widget=widget)
            if ok:
                try:
                    if camera_model.get_property("id", None) in self.list_player:
                        if stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                            del self.list_player[camera_model.get_property("id", None)][stream_type]
                        else:
                            self.list_player[camera_model.get_property("id", None)][stream_type].remove(player)
                except Exception as e:
                    logger.info(f'unregister_player error')
                    
    def create_player(self,camera_model:CameraModel = None,stream_type = CommonEnum.StreamType.MAIN_STREAM):
        start_time = time.time()
        end_time = time.time()
        logger.debug(f'end_time - start_time = {end_time-start_time}')
        # lưu player vào self.list_player nhằm có thể tái sử dụng frame trong trường camera được open ở 2 vị trí grid khác nhau
        if stream_type != CommonEnum.StreamType.VIDEO_STREAM:
            player = LiveStreamPlayer(camera_id=camera_model.get_property("id", None),camera_model=camera_model,stream_type=stream_type)
            if camera_model.get_property("id", None) in self.list_player:
                ##################################################
                # Xử lý vấn đề bất đồng bộ
                # Case open 1 camera trên virtual window -> có 2 widget cần đăng ký player, do quá trình xử lý việc đăng ký player trong multi thread lên cần cập nhật registered_widgets để quá trình hủy đăng ký player được đồng bộ
                if stream_type in self.list_player[camera_model.get_property("id", None)]:
                    temp_player = self.list_player[camera_model.get_property("id", None)][stream_type]
                    player.registered_widgets.extend(temp_player.registered_widgets)
                ###################################################
                self.list_player[camera_model.get_property("id", None)][stream_type] = player

            else:
                self.list_player[camera_model.get_property("id", None)] = {stream_type:player}

            # Connect to CameraGridItem when live stream player is created
            if stream_type == CommonEnum.StreamType.MAIN_STREAM:
                self.list_player[camera_model.get_property("id", None)] = {stream_type:player}
        else:
            player = VideoPlayer(camera_id=camera_model.get_property("id", None),camera_model=camera_model,stream_type=stream_type)
            if camera_model.get_property("id", None) in self.list_player:
                if stream_type in self.list_player[camera_model.get_property("id", None)]:
                    self.list_player[camera_model.get_property("id", None)][stream_type].append(player)
                else:
                    self.list_player[camera_model.get_property("id", None)][stream_type] = [player]
            else:
                self.list_player[camera_model.get_property("id", None)] = {stream_type:[player]}
        return player


    def get_player(self,camera_model:CameraModel = None, stream_type = CommonEnum.StreamType.MAIN_STREAM):
        # logger.info(f'get_player: stream_type = {stream_type}')
        if stream_type != CommonEnum.StreamType.VIDEO_STREAM:
            if camera_model is not None:
                if camera_model.get_property("id", None) in self.list_player:
                    logger.info(f'get_player: stream_type = {self.list_player[camera_model.get_property("id", None)]}')
                    if stream_type in self.list_player[camera_model.get_property("id", None)]:
                        return self.list_player[camera_model.get_property("id", None)][stream_type]
                    else:
                        return self.create_player(camera_model=camera_model,stream_type=stream_type)
                else:
                    return self.create_player(camera_model=camera_model,stream_type=stream_type)
        else:
            return self.create_player(camera_model=camera_model,stream_type=stream_type)
    
    def remove_player(self,server:ServerInfoModel):
        # remove player by server_ip
        list_id = []
        for id,item in self.list_player.items():
            for stream_type, player in item.items():
                if stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                    for p in player:
                        if p.camera_model.get_property("server_ip",None) == server.data.server_ip:
                            p.stop_capture()
                            if id not in list_id:
                                list_id.append(id)
                    break
                else:
                    if player.camera_model.get_property("server_ip",None) == server.data.server_ip:
                        player.stop_capture()
                        if id not in list_id:
                            list_id.append(id)
        for id in list_id:
            del self.list_player[id]
    
    def cleanup(self):
        """Clean up all video captures and resources"""
        with self._cleanup_lock:
            try:
                # Stop all threads
                for _ in range(len(self.threads or [])):
                    self.input_queue.put(None)
                
                # Clean up all video captures - use complete thread stop
                for camera_id in list(self.list_player.keys()):
                    for stream_type in list(self.list_player[camera_id].keys()):
                        try:
                            player = self.list_player[camera_id][stream_type]
                            if isinstance(player, list):
                                for p in player:
                                    p.stop_capture()
                            else:
                                player.stop_capture()
                        except Exception as e:
                            logger.error(f'Error cleaning up video capture {camera_id}: {e}')
                
                self.list_player.clear()
                self.list_widgets.clear()
                
                # Clear queue
                while not self.input_queue.empty():
                    try:
                        self.input_queue.get_nowait()
                    except Queue.Empty:
                        break
                        
            except Exception as e:
                logger.error(f'Error during VideoPlayerManager cleanup: {e}')

    def __del__(self):
        """Ensure cleanup on destruction"""
        try:
            self.cleanup()
        except Exception as e:
            logger.error(f'Error during VideoPlayerManager destruction: {e}')

    def restart_all_players(self):
        """Check and update players with current stream status"""
        check_count = 0
        logger.debug(f'Checking all players stream status: Found {len(self.list_player)} cameras')

        for camera_id in list(self.list_player.keys()):
            try:
                # Check current stream status for all streams of this camera
                from src.common.model.camera_model import camera_model_manager,CameraModel
                camera_model = camera_model_manager.get_camera_model(id=camera_id)
                self.check_camera_stream_status(camera_model)
                check_count += 1
                logger.debug(f'Checked stream status for camera {camera_id}')
            except Exception as e:
                logger.error(f'Error checking camera {camera_id} stream status: {e}')

        logger.debug(f'Total cameras checked: {check_count}')

    def check_camera_stream_status(self, camera_model: CameraModel):
        """Check current stream status and update if needed"""
        # Cache camera_id to avoid repeated property access
        camera_id = camera_model.get_property("id", None)
        print(f'🎯 [VIDEO_PLAYER_MANAGER] check_camera_stream_status: camera_id = {camera_id}')
        # Check permission for PHONE cameras
        if camera_model.isPhoneCamera():
            permission_granted = camera_model.get_property("permissionGranted", False)
            print(f'🎯 [VIDEO_PLAYER_MANAGER] check_camera_stream_status: permission_granted = {permission_granted}')
            if not permission_granted:
                print(f'🎯 [VIDEO_PLAYER_MANAGER] check_camera_stream_status: permission_granted = {permission_granted}')
                return
        print(f'🎯 [VIDEO_PLAYER_MANAGER] check_camera_stream_status: camera_id = {camera_id}')

        if camera_id not in self.list_player:
            print(f'🎯 [VIDEO_PLAYER_MANAGER] check_camera_stream_status: camera_id = {camera_id} not found for status check')
            logger.warning(f'Camera {camera_id} not found for status check')
            return

        # Get live stream players (excluding VIDEO_STREAM) with direct dict access
        camera_players = self.list_player[camera_id]
        live_stream_players = {st: players for st, players in camera_players.items()
                             if st != CommonEnum.StreamType.VIDEO_STREAM}

        if not live_stream_players:
            logger.debug(f'No live streams found for camera {camera_id}')
            return

        logger.debug(f'Checking stream status for camera {camera_id}')

        # Cache controller to avoid repeated lookups
        if not hasattr(self, 'controller') or self.controller is None:
            from src.common.controller.controller_manager import controller_manager
            self.controller = controller_manager.get_controller(server_ip=camera_model.get_property("server_ip", None))

        def check_streams_response(response, _):
            try:
                print(f'🎯 [VIDEO_PLAYER_MANAGER] check_streams_response: response = {response}')
                data = response.json()
                logger.debug(f'Stream status response for camera {camera_id}: {data}')

                # Single pass to create both connected indices and streams dict
                connected_indices = set()
                connected_streams_dict = {}

                for stream in data:
                    if stream.get("state") == "CONNECTED" or stream.get("state") is None:
                        stream_index = stream["index"]
                        connected_indices.add(stream_index)
                        connected_streams_dict[stream_index] = stream.get("url")

                if connected_indices is not None and len(connected_indices) > 0 and live_stream_players is not None and len(live_stream_players) > 0:
                    # Process each live stream type with direct lookup
                    for stream_type, players in live_stream_players.items():
                        # Normalize players to list for uniform processing
                        target_players = players if isinstance(players, list) else [players]
                        
                        is_stream_type_available = stream_type in connected_indices

                        stream_type_available = stream_type if is_stream_type_available else connected_indices.pop()
                        # Get stream URL once for this stream type
                        stream_url = connected_streams_dict.get(stream_type_available)
                        # Notify all players of this stream type
                        for player in target_players:
                            if player.stream_link is None and stream_url is not None or stream_url != player.stream_link:
                                player.on_stream_link_changed(stream_url)
                            else:
                                player.on_server_state_changed("CONNECTED")
            except Exception as e:
                logger.error(f'Error checking streams status for camera {camera_id}: {e}')

        print(f'🎯 [VIDEO_PLAYER_MANAGER] check_streams_response: camera_id = {camera_id}')
        logger.info(f'check_streams_response: camera_id = {camera_id}')
        # Single API call to get all stream status
        self.controller.get_stream_url_thread(
            cameraId=camera_id,
            callback=check_streams_response
        )

    def notify_camera_state_change(self, camera_model, new_state):
        print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: camera_model = {camera_model}, new_state = {new_state}')
        """Notify all players of a camera about state change"""
        camera_id = camera_model.get_property("id", None)
        logger.info(f'notify_camera_state_change: camera={camera_id}, state={new_state}')

        # PHONE camera permission check
        if camera_model.isPhoneCamera() and new_state == "CONNECTED":
            permission_granted = camera_model.get_property("permissionGranted", False)
            print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: permission_granted = {permission_granted}')
            logger.info(f'notify_camera_state_change: permission_granted = {permission_granted}')
            if not permission_granted:
                print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: permission_granted = {permission_granted}')
                logger.error(f'notify_camera_state_change: permission_granted')
                return
        print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: self.list_player = {self.list_player}')
        if camera_id in self.list_player:
            for stream_type in self.list_player[camera_id]:
                try:
                    if stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                        player = self.list_player[camera_id][stream_type]
                        print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: player = {player}')
                        if hasattr(player, 'on_server_state_changed'):
                            print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: player.stream_link = {player.stream_link}')
                            if player.stream_link is not None:
                                print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: player.stream_link is not None')
                                player.on_server_state_changed(new_state)
                            else:
                                print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: player.stream_link is None')
                                logger.error(f'notify_camera_state_change: permission_granted - camera_model = {camera_model}')
                                self.check_camera_stream_status(camera_model)
                except Exception as e:
                    print(f'🎯 [VIDEO_PLAYER_MANAGER] notify_camera_state_change: Error notifying player about state change for camera {camera_id}: {e}')
                    logger.error(f'Error notifying player about state change for camera {camera_id}: {e}')

video_player_manager = VideoPlayerManager.get_instance()

